import Cookies from "js-cookie";
import { Layout } from "antd";
import { Component } from "react";
import styled from "styled-components";


import HeaderNav from "./header";
import HeadContent from "./header/content";

// css-js start ↓↓↓
const HeadNavLayout = styled(Layout)`
  width: 100%;
  height: 100%;
`
// css-js end   ↑↑↑



export default class BaseLayout extends Component {
  componentDidMount(props) {
    // 暂时禁用登录检查
    // const token = Cookies.get("token");
    // if (typeof token == "undefined") {
    //   window.location.replace(
    //     process.env.REACT_APP_OA_HOST +
    //     "/r/w?cmd=API_com.cm.cheetah.httpapi.login&forward=" +
    //     process.env.REACT_APP_HOST +
    //     "/login"
    //   );
    // }
    
    // 开发环境下设置默认cookie，模拟登录状态
    const devUserEmail = "<EMAIL>";
    Cookies.set("user_email", devUserEmail);
    Cookies.set("token", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVc2VyRW1haWwiOiJ0YW5taW5neXVAY21jbS5jb20iLCJVc2VyTmFtZSI6IuiwreaYjuWuhyIsImV4cCI6MTc1MjYzMTYxOCwiaWF0IjoxNzUyMDI2ODE4LCJzdWIiOiJ3Zm8ifQ.alRZ-2gN4XiPmyUIIwneqhl58KN3woMOop2POuSKVcg");
    Cookies.set("sid", "5abc08c7-c318-478d-91f4-a01d6c6e602b");
    console.log(`开发环境：设置默认用户邮箱为 ${devUserEmail}`);
    
    console.log('已临时禁用登录检查');
  }

  render() {
    return (
      <HeadNavLayout>
        <HeaderNav />
        <HeadContent />
      </HeadNavLayout>
    );
  }
}




/src/request

import axios from "axios";
import Cookies from "js-cookie";
import { message } from "antd";

const service = axios.create({
  baseURL: process.env.REACT_APP_API_HOST,
});

// 请求拦截器
service.interceptors.request.use(
  (config) => {
    // 仅使用后端允许的请求头
    config.headers = {
      "Content-Type": "application/json",
      "Authorization": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVc2VyRW1haWwiOiJ0YW5taW5neXVAY21jbS5jb20iLCJVc2VyTmFtZSI6IuiwreaYjuWuhyIsImV4cCI6MTc1MjYzMTYxOCwiaWF0IjoxNzUyMDI2ODE4LCJzdWIiOiJ3Zm8ifQ.alRZ-2gN4XiPmyUIIwneqhl58KN3woMOop2POuSKVcg",
      // "Cookies": "sid=9e58158e-0b09-449f-8d7d-eec67c1530fd; token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVc2VyRW1haWwiOiJ0YW5taW5neXVAY21jbS5jb20iLCJVc2VyTmFtZSI6IuiwreaYjuWuhyIsImV4cCI6MTc1MjAyNjU4NiwiaWF0IjoxNzUxNDIxNzg2LCJzdWIiOiJ3Zm8ifQ.cmoDEx9Lrr_Did9SCYX1QzEgtcaOTesmBpcJlgnFuxE; user_email=<EMAIL>",
      "User-Agent": "Mozilla/5.0",
      "Keep-Alive": "timeout=5"
      // 只使用允许的请求头，移除了所有不允许的头信息
    };
    
    console.log('已将请求头调整为符合CORS要求的格式');
    return config;
  }
);

// 响应拦截器
service.interceptors.response.use((response) => {
  if (response.status) {
    if (response.data.resp_common.ret !== 0) {
      message.error("后台错误：" + response.data.resp_common.msg);
      return null
    } else {
      return response.data;
    }
    // switch (response.status) {
    //   //todo 需要细化处理
    //   case 200:
    //     return response.data;
    //   case 401:
    //     //未登录处理方法
    //     console.log(401);
    //     break;
    //   case 403:
    //     //token过期处理方法
    //     console.log(403);
    //     break;
    //   default:
    //     console.log("default");
    // }
  } else {
    return response;
  }
});


//最后把封装好的axios导出
export default service;

export const cmdbService = (uri, data) => fetch(process.env.REACT_APP_CMDB_API_HOST + uri, {
  method: "POST",
  headers: new Headers({
    'Content-Type': 'application/json',
    'Authorization': "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVc2VyRW1haWwiOiJ0YW5taW5neXVAY21jbS5jb20iLCJVc2VyTmFtZSI6IuiwreaYjuWuhyIsImV4cCI6MTc1MjYzMTYxOCwiaWF0IjoxNzUyMDI2ODE4LCJzdWIiOiJ3Zm8ifQ.alRZ-2gN4XiPmyUIIwneqhl58KN3woMOop2POuSKVcg",
    'User-Agent': "Mozilla/5.0",
    'Keep-Alive': "timeout=5"
    // 只使用允许的请求头
  }),
  body: JSON.stringify(data)
}).then(response => {
  if (response.ok) {
    return response.json();
  } else {
    message.error("后台错误：" + response);
  }
})
