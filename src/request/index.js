import axios from "axios";
import Cookies from "js-cookie";
import { message } from "antd";

const service = axios.create({
  baseURL: process.env.REACT_APP_API_HOST,
});

// 请求拦截器
service.interceptors.request.use(
  (config) => {
    const token = Cookies.get("token");
    //设置请求头，在请求头中添加token
    config.headers = {
      "Content-Type": "application/json",
      Authorization: token,
    };
    return config;
  }
);

// 响应拦截器
service.interceptors.response.use((response) => {
  if (response.status) {
    if (response.data.resp_common.ret !== 0) {
      message.error("后台错误：" + response.data.resp_common.msg);
      return null
    } else {
      return response.data;
    }
    // switch (response.status) {
    //   //todo 需要细化处理
    //   case 200:
    //     return response.data;
    //   case 401:
    //     //未登录处理方法
    //     console.log(401);
    //     break;
    //   case 403:
    //     //token过期处理方法
    //     console.log(403);
    //     break;
    //   default:
    //     console.log("default");
    // }
  } else {
    return response;
  }
});


//最后把封装好的axios导出
export default service;

export const cmdbService = (uri, data) => fetch(process.env.REACT_APP_CMDB_API_HOST + uri, {
  method: "POST",
  headers: new Headers({
    'Content-Type': 'application/json',
    'Authorization': Cookies.get("token")
  }),
  body: JSON.stringify(data)
}).then(response => {
  if (response.ok) {
    return response.json();
  } else {
    message.error("后台错误：" + response);
  }
})
