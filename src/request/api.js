//导入我们封装好的axios
// import service from "./index";
import service from "./index";
import { cmdbService } from "./index";

// 后端鉴权
export const requestAuth = (info) => service.post("/auth/oa/sid-token", info);

// ********************** 审批流转 **********************
// 页面审批
export const requestFlowApproval = (info) => service.post("/order/approval", info);
// 审批人变更

// ********************** 工单信息 **********************
export const requestFlowAuditTurn = (info) => service.post("/order/flow-audit/turn", info);
// 获取工单详情
export const requestOrderDetail = (info) => service.post("/order/detail", info);
// 获取我的历史工单
export const requestMyDoneOrder = (info) =>
	service.post("/order/my-done", info);
// 获取正在进行中的工单
export const requestMyDoingOrder = (info) =>
	service.post("/order/my-doing", info);

// 获取当前用户工单列表
export const requestMyAuditOrderWithSearch = async (params) =>
{
  try {
    // 将参数转换为后端期望的格式
    const processedParams = {
      page: Number(params.pageNum) || 1,          // 数字类型，从1开始
      page_size: Number(params.pageSize) || 10,   // 数字类型
      order_id: params.orderId || '',             // 字符串类型
      title: params.title || '',                  // 字符串类型
      applied_start_date: params.appliedStartDate || null, // 直接传递字符串格式日期
      applied_end_date: params.appliedEndDate || null,     // 直接传递字符串格式日期
      order_types: params.orderTypes || [],       // 数组类型
    };

    const response = await service.post("/order/my-audit", processedParams);
    return response; // service拦截器已经返回了正确的数据结构，不需要再次访问.data
  } catch (error) {
    console.error('API request for my audit orders failed:', error);
    // 统一错误处理，返回一个匹配新接口结构的错误响应
    return {
      resp_common: {
        ret: -1,
        msg: error.response?.data?.message || '网络或服务器错误',
        request_id: 'error'
      },
      total: 0,
      orders: [],
    };
  }
};

/**
 * 获取工单类型列表（迭代三使用）
 * @returns {Promise<Object>} 工单类型列表
 */
export const requestOrderTypes = async (info = {}) =>
{
  try {
    const response = await service.post("/order/types", info);
    return response.data;
  } catch (error) {
    console.error('API request for order types failed:', error);
    return {
      code: error.response?.status || -1,
      message: error.response?.data?.message || '获取工单类型失败',
      data: [],
    };
  }
};

export const requestUpdateOrderRemark = (info) => service.post("/order/update/remark", info);

// ********************** 金山云接口 **********************
// 获取金山云城市
export const requestKsRegion = (info) => service.post("/kingsoft/region", info);
// 获取金山云地区
export const requestKsZone = (info) =>
	service.post("/kingsoft/region/zone", info);
// 获取金山云镜像
export const requestKsImage = (info) => service.post("/kingsoft/image", info);
// 获取金山云硬件规格配置
export const requestKsHardwareSpecType = (info) =>
	service.post("/kingsoft/server/hardware-spec-type", info);
// 获取金山云硬件规格配置
export const requestKsHardwareSpec = (info) =>
	service.post("/kingsoft/server/hardware-spec", info);
// 提交金山云服务表单
export const requestKsServerPrice = (info) =>
	service.post("/kingsoft/server/price", info);
// 提交金山云服务表单
export const requestKsServerApply = (info) =>
	service.post("/order/server/ks/apply", info);

// ********************** jumpserver **********************
// 获取非法服务器ip和拥有者email
export const requestServerOwnerEmail = (info) =>
	service.post("/jumpserver/getServerOwner", info);
// 提交跳板机权限申请表单
export const requestJumpServerAccountApply = (info) =>
	service.post("/order/server/jump/impower", info);

// 获取运维人员
export const requestOpsMember = (info) => service.post("/ops/member", info);
// 获取运维人员-职责映射关系
export const requestOpsMemberDutySpectrum = (info) => service.post("/ops/member/duty-spectrum", info);
// 获取dba
export const requestOpsDBA = (info) => service.post("/ops/dba", info);
// 领导邮箱
export const requestLeaderEmail = (info) => service.post("/commoninfo/leader-email", info);
export const requestSuperLeaderEmails = (info) => service.post("/commoninfo/super-leader-email", info);
export const requestOnlySuperLeaderEmails = (info) => service.post("/commoninfo/only-super-leader-email", info);
export const requestUserLeaderEmail = (info) => service.post("/commoninfo/user-leader-email", info);
export const requestThreeLevelLeaderEmails = (info) => service.post("/commoninfo/three-level-leader-email", info)
// 校验抄送人邮箱
export const validateUser = async (email) => {
  try {
    const response = await service.post("/order/validate-user", { email });
    if (response && response.resp_common && response.resp_common.ret === 0) {
      return Promise.resolve(response);
    }
    // 业务失败，返回具体的错误信息
    return Promise.reject(response?.resp_common?.msg || '校验失败');
  } catch (error) {
    // 网络或代码异常
    return Promise.reject('校验服务异常，请稍后重试');
  }
};

// 提交通用工单
export const requestCommonOrder = (info) => service.post("/order/common", info);


// ********************** sql 审计 **********************
// 获取数据库实例
export const requestDbInstanceInfo = (info) => service.post("/sqlaudit/db-instance", info);
// 校验SQL语句
export const requestSqlCheck = (info) => service.post("/sqlaudit/sql/check", info);
// 校验sql附件
export const requestSqlFileCheck = (info) => service.post("/sqlaudit/sql-attachment/check", info);
// 校验sql附件
export const requestSqlFileCheckResult = (info) => service.post("/sqlaudit/sql-attachment/check/result", info);

// 获取数据库实例
export const requestAuditDbInstance = (info) => service.post("/sqlaudit/audit-db-instance", info);


// ********************** cdn **********************
export const requestCdnFlush = (info) => service.post("/order/cdn/flush", info);
export const requestCdnFlushHistory = (info) => service.post("/cdn/flush/list", info);


// ********************** 腾讯云 **********************
// 获取cos权限
export const requestTxCamCosAuth = (info) => service.post("/tengxun/cam/cos/auth", info);
export const requestTxDNSApply = (info) => service.post("/domain/resolve", info);
export const requestTxDomainCheck = (info) => service.post("/tengxun/dns/seconday/check", info);
// 地区
export const requestTxRegion = (info) => service.post("/tengxun/region", info);
// 可用区
export const requestTxZone = (info) => service.post("/tengxun/zone", info);
// 服务器可售规格
export const requestTxServerFamilyTypeConf = (info) => service.post("/tengxun/server/family-type-conf", info);
// 操作系统镜像
export const requestTxOsImage = (info) => service.post("/tengxun/os-image", info);
// 系统盘配额
export const requestTxSysDisk = (info) => service.post("/tengxun/disk/sys", info);
// 数据盘配额
export const requestTxDataDisk = (info) => service.post("/tengxun/disk/data", info);
// 服务器一年价格预估
export const requestServerOneYearPrice = (info) => service.post("/tengxun/server/one-year-price", info);
// 获取mysql规格
export const postTxMysqlConfs = (info) => service.post("/tengxun/db/product", info);
// 获取mysql价格
export const postTxMysqlPrice = (info) => service.post("/tengxun/db/price", info);

// 获取redis规格
export const postTxRedisConfs = (info) => service.post("/tengxun/rds/product", info);
// 获取redis价格
export const postTxRedisPrice = (info) => service.post("/tengxun/rds/price", info);

// ********************** 华为云 **********************
export const requestHwCdnApply = (info) => service.post("/cdn/domain/create", info);

// ********************** 多云 **********************
export const postCloudServerSellConf = (info) => service.post("/cloudvendor/server/sell-conf", info);
export const postCloudDiskSellConf = (info) => service.post("/cloudvendor/disk/sell-conf", info);
export const postCloudImageConf = (info) => service.post("/cloudvendor/image/config", info);
export const postCloudServerPriceOrder = (info) => service.post("/cloudvendor/server/year-price", info);
export const postBuyCloudServer = (info) => service.post("/order/cloud/server/buy", info);
export const postResourceDelete = (info) => service.post("/order/resouce/delete", info);
export const postResourceDeleteConfirm = (info) => service.post("/recyclebin/confirm", info);
export const postCloudMysqlSellConf = (info) => service.post("/cloudvendor/mysql/sell-conf", info);
export const postCloudRedisSellConf = (info) => service.post("/cloudvendor/redis/sell-conf", info);
export const postCloudMysqlPriceOrder = (info) => service.post("/cloudvendor/mysql/year-price", info);
export const postCloudRedisPriceOrder = (info) => service.post("/cloudvendor/redis/year-price", info);
export const postBuyCloudMysql = (info) => service.post("/order/cloud/mysql/buy", info);
export const postBuyCloudRedis = (info) => service.post("/order/cloud/redis/buy", info);

export const postTokenApply = (info) => service.post("/order/token/apply", info);

// ********************** token **********************
export const requestNewOrRefreshLongTermToken = (info) => service.post("/auth/long-term-token/new-refresh", info);
export const requestViewLongTermToken = (info) => service.post("/auth/long-term-token/view", info);

// ********************** cmdb **********************
export const postModelTree = (info) => cmdbService("/model/tree/nested-array-map", info);
export const postModelTree2 = (info) => cmdbService("/model/multi-branch-tree", info);
export const postModelParentPath = (info) => cmdbService("/model/tree/parent-path", info);
export const postNewModelNode = (info) => service.post("/order/model/new-node", info);
export const postNewModelChildNode = (info) => service.post("/order/model/new-child-node", info);
export const postModifyModelChildNode = (info) => service.post("/order/model/modify-node", info);
export const postDeleteModelChildNode = (info) => service.post("/order/model/delete-node", info);
export const postDragModelChildNode = (info) => service.post("/order/model/drag-node", info);
export const postConfApportion = (info) => service.post("/order/model/cost/apportion-conf", info);
export const postResourceCommon = (info) => cmdbService("/resource/common", info);


// ********************** 运维 **********************
export const postOpsLeader = (info) => service.post("/ops/leader", info);

// ********************** 证书 **********************
export const requestUpdateCert = (info) => service.post("/ops/upload/cert", info);
// ********************** 域名 **********************
export const postBuyDomain = (info) => service.post("/order/domain/buy", info);

// ********************** 资源变更 **********************
export const requestUpdateCMDBServiceOwner = (info) => service.post("/order/cmdb/resource/update-service-owner", info);