import { Component } from "react";
import { Link } from "react-router-dom";
import { Layout, Menu } from "antd";
import {
  DatabaseTwoTone,
  ContactsOutlined,
  DatabaseOutlined,
  SnippetsOutlined,
  ConsoleSqlOutlined,
  RedoOutlined,
  ApartmentOutlined,
  CloudSyncOutlined,
  GlobalOutlined,
  DeleteOutlined,
  KeyOutlined,
  EditOutlined
} from "@ant-design/icons";
import styled from "styled-components";
import SidebarContent from "./content";

const { Sider } = Layout;
const { SubMenu } = Menu;

// css-js start ↓↓↓
const SideNavLayout = styled(Layout)`
  /* padding: 10px 10px; */
  background: #F5F5F5;
  height: 100%;
`
const LeftSide = styled(Sider)`
  min-width: 11.5vw !important;
  height: 100%;
`
const LeftSideMenu = styled(Menu)`
  height: 100%;
`
// css-js end   ↑↑↑

const IconStyle = {
  style: { fontSize: '0.95vw' }
}

export default class SidebarNav extends Component {
  render() {
    return (
      <SideNavLayout>
        <LeftSide>
          <LeftSideMenu
            mode="inline"
            defaultSelectedKeys={["1"]}
            defaultOpenKeys={["sub1"]}
          >
            <Menu.Item key="common" icon={<SnippetsOutlined {...IconStyle} />}>
              <Link to="/new-order/common">通用工单</Link>
            </Menu.Item>
            <Menu.Item key="sql" icon={<ConsoleSqlOutlined {...IconStyle} />}>
              <Link to="/new-order/sql">sql审计执行</Link>
            </Menu.Item>
            <Menu.Item key="resource-dns" icon={<GlobalOutlined {...IconStyle} />}>
              <Link to="/new-order/dns">域名解析</Link>
            </Menu.Item>
            <SubMenu key="cdn" icon={<CloudSyncOutlined {...IconStyle} />} title="cdn">
              <Menu.Item key="cdn-flush" icon={<RedoOutlined style={{ "color": "#1890ff" }} />}>
                <Link to="/new-order/cdn-flush">cdn刷新</Link>
              </Menu.Item>
              <Menu.Item key="resource-cdn" icon={<DatabaseTwoTone />}>
                <Link to="/new-order/cdn">cdn申请</Link>
              </Menu.Item>
            </SubMenu>
            <SubMenu key="account" icon={<ContactsOutlined {...IconStyle} />} title="账号申请">
              <Menu.Item key="account-server" icon={<DatabaseTwoTone />}>
                <Link to="/new-order/server-account">服务器账号申请</Link>
              </Menu.Item>
            </SubMenu>
            <SubMenu key="resource" icon={<DatabaseOutlined {...IconStyle} />} title="资源申请">
              <Menu.Item key="resource-server" icon={<DatabaseTwoTone />}>
                <Link to="/new-order/server">云服务器申请</Link>
              </Menu.Item>
              <Menu.Item key="resource-redis" icon={<DatabaseTwoTone />}>
                <Link to="/new-order/redis">Redis申请</Link>
              </Menu.Item>
              <Menu.Item key="resource-mysql" icon={<DatabaseTwoTone />}>
                <Link to="/new-order/mysql">Mysql申请</Link>
              </Menu.Item>
              <Menu.Item key="resource-domain" icon={<DatabaseTwoTone />}>
                <Link to="/new-order/domain">域名申请</Link>
              </Menu.Item>
            </SubMenu>
            <Menu.Item key="resource-del" icon={<DeleteOutlined {...IconStyle} />}>
              <Link to="/new-order/resource-del">资源销毁</Link>
            </Menu.Item>
            <Menu.Item key="k8stoken" icon={<KeyOutlined {...IconStyle} />}>
              <Link to="/new-order/k8stoken">token申请</Link>
            </Menu.Item>
            <SubMenu key="resource-update" icon={<EditOutlined {...IconStyle} />} title="资源变更">
              <Menu.Item key="serviceOwner-update" icon={<EditOutlined style={{ "color": "#1890ff" }} />}>
                <Link to="/new-order/service_owner/update">资源负责人变更</Link>
              </Menu.Item>
            </SubMenu>
            <Menu.Item key="cmdb-model" icon={<ApartmentOutlined {...IconStyle} />}>
              <Link to="/new-order/cmdb/model-manager">业务树管理</Link>
            </Menu.Item>
            <SubMenu key="cert" icon={<DatabaseOutlined {...IconStyle} />} title="证书管理">
              <Menu.Item key="cert-upload" icon={<DatabaseTwoTone />}>
                <Link to="/new-order/cert/upload">证书上传</Link>
              </Menu.Item>
            </SubMenu>

          </LeftSideMenu>
        </LeftSide>
        <SidebarContent />
      </SideNavLayout>
    );
  }
}
