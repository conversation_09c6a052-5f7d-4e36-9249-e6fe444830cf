import { Component } from "react";
import { Layout } from "antd";
import styled from "styled-components";

import SidebarRouter from "@/router/headerRouter/sidebarRouter";
const { Content } = Layout;

// css-js start ↓↓↓
const SideNavContent = styled(Content)`
  padding: 1vw 0vw; 
  width: "100%";
  height: "100%";
`
// css-js end   ↑↑↑


export default class SidebarContent extends Component {
  render() {
    return (
      <SideNavContent>
        <SidebarRouter />
      </SideNavContent>
    );
  }
}
