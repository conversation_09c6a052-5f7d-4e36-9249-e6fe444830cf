import Cookies from "js-cookie";
import { Layout } from "antd";
import { Component } from "react";
import styled from "styled-components";


import HeaderNav from "./header";
import HeadContent from "./header/content";

// css-js start ↓↓↓
const HeadNavLayout = styled(Layout)`
  width: 100%;
  height: 100%;
`
// css-js end   ↑↑↑



export default class BaseLayout extends Component {
  componentDidMount(props) {
    // 取token，若无则跳转登录
    const token = Cookies.get("token");
    if (typeof token == "undefined") {
      window.location.replace(
        process.env.REACT_APP_OA_HOST +
        "/r/w?cmd=API_com.cm.cheetah.httpapi.login&forward=" +
        process.env.REACT_APP_HOST +
        "/login"
      );
    }
  }

  render() {
    return (
      <HeadNavLayout>
        <HeaderNav />
        <HeadContent />
      </HeadNavLayout>
    );
  }
}
