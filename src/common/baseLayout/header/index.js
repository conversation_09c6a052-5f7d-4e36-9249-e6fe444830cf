import { Component } from "react";
import { Layout, <PERSON>u, Popover, Button, Modal, Input, InputNumber, message } from "antd";
import { NavLink } from "react-router-dom";
import Cookies from "js-cookie";

import styled from "styled-components";
import { UserOutlined, PoweroffOutlined } from "@ant-design/icons";

import { headerRules } from "@/router/headerRouter";
import withRouter from "@/util/withRouter";
import { NewOrderUrlSuffix, DoingOrderUrlSuffix, DoneOrderUrlSuffix, AuditOrderUrlSuffix } from "@/global";
import { requestNewOrRefreshLongTermToken, requestViewLongTermToken } from "@/request/api";
import "@/common/global";
import { Domain } from "@/common/wildcardlDomain";

const { Header } = Layout;
// css-js start ↓↓↓
const WfoHeader = styled(Header)`
    padding: 0 30px;
`
const Logo = styled.img`
    float: left;
    width: 190px;
    height: 100%;
    padding: 15px 30px 15px 0;
`;
const UserBox = styled.div`
    float: right;
    width: 210px;
    height: 100%;
    font-size: medium;
    color: white;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: center;
`
const UserDiv = styled.div`
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: center;
    align-items: center;
`

const UserLogo = styled(UserOutlined)`
    display: flex;
    align-items: center;
    font-size: x-large;
`
const UserContentDiv = styled.div`
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
`
const UserContentButton = styled(Button)`
    width: 150px;
    margin: 3px 0px;
`
const UserTokenDiv = styled.div`
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
`
const UserTokenRow = styled.div`
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: space-between;
    align-items: center;
    white-space: nowrap;
    margin-top: 10px;
`
// css-js end   ↑↑↑





class HeaderNav extends Component {
    componentDidMount() {
        if (this.props.location.pathname === "/") {
            this.setState({
                selectNav: ["new-order"]
            })
            return setTimeout(() => { this.props.navigate(NewOrderUrlSuffix) }, 0)
        } else if (this.props.location.pathname === NewOrderUrlSuffix) {
            this.setState({
                selectNav: ["new-order"]
            })
        } else if (this.props.location.pathname === DoingOrderUrlSuffix) {
            this.setState({
                selectNav: ["doing-order"]
            })
        } else if (this.props.location.pathname === DoneOrderUrlSuffix) {
            this.setState({
                selectNav: ["done-order"]
            })
        } else if (this.props.location.pathname === AuditOrderUrlSuffix) {
            this.setState({
                selectNav: ["audit-order"]
            })
        }
    }
    componentDidUpdate() {
        if (!global.programRedirect) {
            return
        }
        if (this.props.location.pathname === "/") {
            this.setState({
                selectNav: ["new-order"]
            })
            return setTimeout(() => { this.props.navigate(NewOrderUrlSuffix) }, 0)
        } else if (this.props.location.pathname === NewOrderUrlSuffix) {
            this.setState({
                selectNav: ["new-order"]
            })
        } else if (this.props.location.pathname === DoingOrderUrlSuffix) {
            this.setState({
                selectNav: ["doing-order"]
            })
        } else if (this.props.location.pathname === DoneOrderUrlSuffix) {
            this.setState({
                selectNav: ["done-order"]
            })
        } else if (this.props.location.pathname === AuditOrderUrlSuffix) {
            this.setState({
                selectNav: ["audit-order"]
            })
        }
        global.programRedirect = false
    }
    state = {
        selectNav: ["new-order"],
        newOrRefreshTokenVisible: false,
        validDay: 30,
        applyMsg: ""
    }
    handleNewOrRefreshClick = () => {
        this.setState(
            { newOrRefreshTokenVisible: true }
        )
    }
    handleValiDayChange = (value) => {
        this.setState({
            validDay: value
        })
    }
    handleApplyMsgChange = (e) => {
        this.setState({
            applyMsg: e.target.value
        })
    }
    handleNewOrRefreshCancel = () => {
        this.setState(
            { newOrRefreshTokenVisible: false }
        )
    }
    handleNewOrRefreshOk = () => {
        if (this.state.applyMsg.length <= 10) {
            message.error("申请理由、用途简述需要超过10个字符。")
            return
        }
        let args = {
            valid_day: this.state.validDay,
            apply_msg: this.state.applyMsg
        }
        requestNewOrRefreshLongTermToken(args).then((data) => {
            if (data !== null) {
                message.success("申请已成功提交！")
            }
            this.setState({
                applyMsg: "",
                validDay: 30
            })
        })
        this.handleNewOrRefreshCancel()
    }
    handleViewTokenClick = () => {
        requestViewLongTermToken({}).then((data) => {
            if (data !== null) {
                Modal.success({
                    content: '查看申请已提交，请前往飞书·运维工单操作查收。',
                });
            }
        })
    }
    handleClick = (e) => {
        this.setState({
            selectNav: e.keyPath
        })
    }
    logOut = () => {
        let cookieAttributes = { domain: Domain }
        // console.log("wfo logout, domain:", Domain)
        Cookies.remove("sid", cookieAttributes);
        Cookies.remove("token", cookieAttributes);
        Cookies.remove("user_email", cookieAttributes);
        // console.log("wfo logout, after token:", Cookies.get("token"))
        window.location.replace(
            process.env.REACT_APP_OA_HOST +
            "/r/w?cmd=API_com.cm.cheetah.httpapi.login&forward=" +
            process.env.REACT_APP_HOST +
            "/login"
        );
    }
    render() {
        return (
            <WfoHeader>
                <Logo alt="运维工单系统" src="/logo/logo.png" />
                <UserBox>
                    <Popover
                        placement="bottom"
                        content={
                            <UserContentDiv>
                                <UserContentButton
                                    type="dashed"
                                    size="small"
                                    onClick={this.handleNewOrRefreshClick}
                                >
                                    申请/创建长效 token
                                </UserContentButton>
                                <UserContentButton
                                    type="dashed"
                                    size="small"
                                    onClick={this.handleViewTokenClick}
                                >
                                    查看我的长效 token
                                </UserContentButton>
                                <UserContentButton
                                    type="dashed"
                                    danger
                                    size="small"
                                    onClick={this.logOut}
                                >
                                    注销登录<PoweroffOutlined />
                                </UserContentButton>
                                <Modal
                                    title="申请/刷新长效token"
                                    open={this.state.newOrRefreshTokenVisible}
                                    onCancel={this.handleNewOrRefreshCancel}
                                    onOk={this.handleNewOrRefreshOk}
                                    cancelText="取消"
                                    okText="申请/刷新"
                                >
                                    <UserTokenDiv>
                                        <Input.TextArea
                                            placeholder="请简述申请理由、用途。"
                                            rows={5}
                                            value={this.state.applyMsg}
                                            onChange={this.handleApplyMsgChange}
                                        />
                                        <UserTokenRow>
                                            <span>
                                                token有效期：
                                                <InputNumber
                                                    size="small"
                                                    min={30}
                                                    max={3650}
                                                    defaultValue={30}
                                                    value={this.state.validDay}
                                                    onChange={this.handleValiDayChange}
                                                />&nbsp;/天
                                            </span>
                                            <Button size="small" type="primary" onClick={() => { this.setState({ validDay: 365 }) }}>1 年</Button>
                                            <Button size="small" type="primary" onClick={() => { this.setState({ validDay: 365 * 3 }) }}>3 年</Button>
                                            <Button size="small" type="primary" onClick={() => { this.setState({ validDay: 365 * 5 }) }}>5 年</Button>
                                            <Button size="small" type="primary" onClick={() => { this.setState({ validDay: 3650 }) }}>10年</Button>
                                        </UserTokenRow>
                                    </UserTokenDiv>
                                </Modal>
                            </UserContentDiv>

                        }
                    >
                        <UserDiv>
                            <span className="user-name">{Cookies.get("user_email")}</span>
                            <UserLogo />
                        </UserDiv>
                    </Popover>

                </UserBox >

                <Menu theme="dark" mode="horizontal" selectedKeys={this.state.selectNav} onClick={this.handleClick}>
                    {headerRules.map((rule) => {
                        return (
                            <Menu.Item key={rule.id}>
                                <NavLink to={rule.id} activeclassname="active" end>
                                    {rule.desc}
                                </NavLink>
                            </Menu.Item>
                        );
                    })}
                </Menu>
            </WfoHeader >
        );
    }
}

export default withRouter(HeaderNav);
