import React, { Component } from 'react';
import { message } from 'antd';
import { requestMyAuditOrderWithSearch } from "../../request/api";
import OrderTable from "../../components/OrderTable";
import LoadingSkeleton from "../../components/common/LoadingSkeleton";
import SearchForm from "../../components/SearchForm";
import OrderPagination from "../../components/OrderPagination";

export default class AuditOrder extends Component {
  state = {
    dataSource: [],
    pageSize: 10,
    pageNum: 1, // 使用pageNum与API保持一致
    total: 0,
    loading: false, // 加载状态
    searchParams: {   // 搜索条件
      orderId: '',
      title: '',
      // 迭代二：日期范围搜索参数
      appliedStartDate: null,
      appliedEndDate: null,
      // 迭代三占位：工单类型筛选
      orderTypes: [],
    },
    orderTypeFilters: [], // 工单类型筛选选项
  };

  componentDidMount() {
    this.requestMyAuditPageOrder(); // 工单类型筛选选项将从首次工单列表请求中提取
  }

  // 工具方法：安全解析工单标题
  parseOrderTitle = (info, orderId) => {
    try {
      if (info && info.trim()) {
        const parsedInfo = JSON.parse(info);
        return parsedInfo.title || '';
      }
    } catch (error) {
      console.warn('解析工单info失败:', error);
    }
    return `工单-${orderId}`;
  };

  // 请求工单数据（支持搜索条件）
  requestMyAuditPageOrder = async () => {
    this.setState({ loading: true });
    const { pageNum, pageSize, searchParams } = this.state;
    
    try {      
      let response;
      
      // 统一使用增强搜索API，支持所有参数
      const requestParams = {
        pageNum,
        pageSize,
        orderId: searchParams.orderId,
        title: searchParams.title,
        appliedStartDate: searchParams.appliedStartDate,
        appliedEndDate: searchParams.appliedEndDate,
        orderTypes: searchParams.orderTypes,
      };
      
      response = await requestMyAuditOrderWithSearch(requestParams);
      
      // 检查响应是否成功（根据新的接口结构）
      if (response.resp_common && response.resp_common.ret === 0) {
        const rawOrders = response.orders || [];
        const orders = rawOrders.map((item, index) => {
          return {
            key: item.order_id || index, // 使用order_id作为key，fallback到index
            orderId: item.order_id,
            title: this.parseOrderTitle(item.info, item.order_id), // 安全解析工单标题
            orderType: item.order_type_name,
            totalNodes: item.total_stage_num,
            currentNode: item.current_stage,
            orderStatus: item.result_desc,
            applicant: item.proposer_email,
            opsOwner: item.ops_owner_email,
            appliedTime: item.apply_datetime,
            role_type: item.role_type, // 添加角色类型字段
          };
        });
        
        // 使用后端返回的order_types更新筛选选项（每次搜索都更新）
        if (response.order_types && response.order_types.length > 0) {
          this.updateOrderTypeFiltersFromResponse(response.order_types);
        } else {
          // 如果后端暂未返回order_types，则使用现有逻辑兼容
          if (!this.state.orderTypeFilters || this.state.orderTypeFilters.length === 0) {
            this.extractOrderTypeFilters(rawOrders);
          }
        }
        
        this.setState({ 
          dataSource: orders, 
          total: response.total || 0,
          loading: false 
        });
      } else {
        // API返回错误码或无数据
        console.warn('API返回异常:', response.resp_common?.msg || '未知错误');
        this.setState({ 
          dataSource: [],
          total: 0,
          loading: false 
        });
        // 如果是首次加载且没有筛选选项，则使用mock数据
        if (!this.state.orderTypeFilters || this.state.orderTypeFilters.length === 0) {
          this.useMockOrderTypeFilters();
        }
      }
    } catch (error) {
      console.error('获取审批工单失败:', error);
      this.setState({ 
        dataSource: [],
        total: 0,
        loading: false 
      });
      // 如果是首次加载且没有筛选选项，则使用mock数据
      if (!this.state.orderTypeFilters || this.state.orderTypeFilters.length === 0) {
        this.useMockOrderTypeFilters();
      }
      // 显示错误提示，但保持用户筛选状态
      this.handleValidationError('数据加载失败，请重试');
    }
  };

  // 处理分页变化
  handlePageChange = (pageNum) => {
    this.setState({ pageNum }, this.requestMyAuditPageOrder);
  };

  // 处理每页显示数量变化
  handlePageSizeChange = (current, size) => {
    this.setState({ pageNum: 1, pageSize: size }, this.requestMyAuditPageOrder);
  };

  // 处理搜索
  handleSearch = (searchParams, hasValidationErrors = false) => {
    console.log('执行搜索:', searchParams, '校验错误:', hasValidationErrors);
    
    // 如果存在校验错误，则不执行搜索
    if (hasValidationErrors) {
      console.warn('搜索条件存在校验错误，不执行搜索');
      return;
    }
    
    // 合并搜索参数，保留已有的工单类型筛选条件
    const newSearchParams = {
      // 保留现有的工单类型筛选条件
      orderTypes: this.state.searchParams.orderTypes,
      // 覆盖其他搜索条件
      orderId: searchParams?.orderId || '',
      title: searchParams?.title || '',
      appliedStartDate: searchParams?.appliedStartDate || null,
      appliedEndDate: searchParams?.appliedEndDate || null,
    };
    
    this.setState({
      searchParams: newSearchParams,
      pageNum: 1, // 搜索时重置到第一页
    }, () => {
      this.requestMyAuditPageOrder();
    });
  };

  // 处理全局提示错误
  handleValidationError = (errorMessage) => {
    message.warn(errorMessage);
  };

  // 处理工单类型筛选变化 - 触发后端请求
  handleOrderTypeFilterChange = (selectedTypes) => {
    const newSearchParams = {
      ...this.state.searchParams,
      orderTypes: selectedTypes
    };
    
    this.setState({
      searchParams: newSearchParams,
      pageNum: 1, // 筛选时重置到第一页
    }, () => {
      this.requestMyAuditPageOrder();
    });
  };

  // 从工单列表中提取工单类型筛选选项
  extractOrderTypeFilters = (orders) => {
    if (!orders || orders.length === 0) {
      this.useMockOrderTypeFilters();
      return;
    }
    
    // 从工单列表中提取所有唯一的工单类型
    const uniqueOrderTypesMap = new Map();
    orders.forEach(order => {
      if (order.order_type && order.order_type_name && 
          order.order_type.trim() && order.order_type_name.trim()) {
        uniqueOrderTypesMap.set(order.order_type.trim(), order.order_type_name.trim());
      }
    });
    
    // 转换为筛选器格式，使用order_type作为value，order_type_name作为text
    const filters = Array.from(uniqueOrderTypesMap.entries())
      .sort((a, b) => a[1].localeCompare(b[1])) // 按中文名称排序
      .map(([orderType, orderTypeName]) => ({
        text: orderTypeName,    // order_type_name作为显示文本
        value: orderType        // order_type作为筛选值
      }));
    
    if (filters.length > 0) {
      this.setState({ orderTypeFilters: filters });
    } else {
      this.useMockOrderTypeFilters();
    }
  };

  // 使用后端返回的order_types更新筛选选项
  updateOrderTypeFiltersFromResponse = (orderTypes) => {
    // orderTypes是后端返回的字符串数组，一般是order_type的值
    // 需要根据实际的数据结构进行调整
    const filters = orderTypes.map(orderType => {
      return {
        text: this.getOrderTypeDisplayName(orderType),
        value: orderType
      };
    });
    
    // 按显示名称排序
    filters.sort((a, b) => a.text.localeCompare(b.text));
    
    this.setState({ orderTypeFilters: filters });
  };

  // 获取工单类型显示名称（映射函数）
  getOrderTypeDisplayName = (orderType) => {
    const typeNameMap = {
      'ks_server_apply': '金山云服务器申请',
      'server_jump_impower': '服务器跳板机账号权限申请',
      'cdn_create_execute': 'CDN创建申请',
      'domain_resolve_execute': '域名解析申请',
      'common': '通用工单',
      'common_super_leader': '通用工单·两级领导',
      'common_three_level_leader': '通用工单·三级领导',
      'common_pointed_approver': '通用工单·指定审批人审批',
      'sql_audit_execute': 'sql审计执行工单',
      'sql_audit_resource_execute': 'sql审计资源执行工单',
      'sql_audit_file_execute': 'sql审计-附件执行工单',
      'sql_audit_resource_file_execute': 'sql审计资源-附件执行工单',
      'pointed_approver': '指定审批人审批',
      'pointed_two_approver': '指定审批人审批·两级',
      'pointed_three_approver': '指定审批人审批·三级',
      'long_term_token_new_refresh': '工单长效token申请/刷新',
      'long_term_token_view': '查看工单长效token',
      'txy_redis_apply': '腾讯云Redis申请',
      'txy_db_apply': '腾讯云Db申请',
      'new_model_node': '新建业务树节点',
      'new_model_child_node': '新建业务树子节点',
      'modify_model_node': '修改业务树节点',
      'delete_model_node': '删除业务树节点',
      'drag_model_node': '移动业务树节点',
      'config_cost_model_apportion': '配置成本模型分摊比例',
      'tx_server_apply': '申请购买腾讯云服务器',
      'token_apply': 'k8s token申请',
      'resource_delete_apply': '云资源销毁申请',
      'domain_apply': '购买域名申请',
      'buy_cloud_server': '购买云服务器',
      'buy_cloud_mysql': '购买云mysql',
      'buy_cloud_redis': '购买云redis',
      'update_cmdb_resource_service_owner': '修改资源业务负责人'
    };
    
    return typeNameMap[orderType] || orderType; // 未找到映射时使用原值
  };

  // 使用模拟工单类型数据
  useMockOrderTypeFilters = () => {
    const mockFilters = [
      { text: '金山云服务器申请', value: 'ks_server_apply' },
      { text: '服务器跳板机账号权限申请', value: 'server_jump_impower' },
      { text: 'CDN创建申请', value: 'cdn_create_execute' },
      { text: '域名解析申请', value: 'domain_resolve_execute' },
      { text: '通用工单', value: 'common' },
      { text: '通用工单·两级领导', value: 'common_super_leader' },
      { text: '通用工单·三级领导', value: 'common_three_level_leader' },
      { text: '通用工单·指定审批人审批', value: 'common_pointed_approver' },
      { text: 'sql审计执行工单', value: 'sql_audit_execute' },
      { text: 'sql审计资源执行工单', value: 'sql_audit_resource_execute' },
      { text: 'sql审计-附件执行工单', value: 'sql_audit_file_execute' },
      { text: 'sql审计资源-附件执行工单', value: 'sql_audit_resource_file_execute' },
      { text: '指定审批人审批', value: 'pointed_approver' },
      { text: '指定审批人审批·两级', value: 'pointed_two_approver' },
      { text: '指定审批人审批·三级', value: 'pointed_three_approver' },
      { text: '工单长效token申请/刷新', value: 'long_term_token_new_refresh' },
      { text: '查看工单长效token', value: 'long_term_token_view' },
      { text: '腾讯云Redis申请', value: 'txy_redis_apply' },
      { text: '腾讯云Db申请', value: 'txy_db_apply' },
      { text: '新建业务树节点', value: 'new_model_node' },
      { text: '新建业务树子节点', value: 'new_model_child_node' },
      { text: '修改业务树节点', value: 'modify_model_node' },
      { text: '删除业务树节点', value: 'delete_model_node' },
      { text: '移动业务树节点', value: 'drag_model_node' },
      { text: '配置成本模型分摊比例', value: 'config_cost_model_apportion' },
      { text: '申请购买腾讯云服务器', value: 'tx_server_apply' },
      { text: 'k8s token申请', value: 'token_apply' },
      { text: '云资源销毁申请', value: 'resource_delete_apply' },
      { text: '购买域名申请', value: 'domain_apply' },
      { text: '购买云服务器', value: 'buy_cloud_server' },
      { text: '购买云mysql', value: 'buy_cloud_mysql' },
      { text: '购买云redis', value: 'buy_cloud_redis' },
      { text: '修改资源业务负责人', value: 'update_cmdb_resource_service_owner' },
    ];
    this.setState({ orderTypeFilters: mockFilters });
  };

  render() {
    const { dataSource, loading, total, pageNum, pageSize, orderTypeFilters, searchParams } = this.state;

    return (
      <div>
        {/* 搜索表单 */}
        <SearchForm 
          onSearch={this.handleSearch}
          loading={loading}
          onValidationError={this.handleValidationError}
        />
        
        {/* 表格区域 */}
        {loading ? (
          <LoadingSkeleton />
        ) : (
          <OrderTable 
            dataSource={dataSource}
            loading={loading}
            orderTypeFilters={orderTypeFilters}
            selectedOrderTypes={searchParams.orderTypes} // 传递当前筛选值
            onOrderTypeFilterChange={this.handleOrderTypeFilterChange}
          />
        )}
        
        {/* 分页组件 */}
        <OrderPagination
          total={total}
          current={pageNum}
          pageSize={pageSize}
          pageSizeOptions={['10', '20', '30', '50', '100']}
          onPageChange={this.handlePageChange}
          onPageSizeChange={this.handlePageSizeChange}
        />
      </div>
    );
  }
}
