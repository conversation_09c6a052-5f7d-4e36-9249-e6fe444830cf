import { Component } from "react";
import { Table } from "antd";
import OrderDetailDrawer from "@/components/OrderDetailDrawer";
import { requestMyDoneOrder } from "@/request/api";
import styled from "styled-components";

import { OrderPagination } from "@/common/styleLayout";

// css-js start ↓↓↓
const DoneTable = styled(Table)`
  margin-top: 2%;
`
// css-js end   ↑↑↑

const columns = [
  {
    title: "单号",
    dataIndex: "orderID",
    key: "orderID",
    fixed: "left",
    align: "center",
  },
  {
    title: "工单类型",
    dataIndex: "orderTypeName",
    key: "orderTypeName",
    align: "center",
  },
  {
    title: "总节点数",
    dataIndex: "totalStageNum",
    key: "totalStageNum",
    align: "center",
  },
  {
    title: "当前节点",
    dataIndex: "currentStage",
    key: "currentStage",
    align: "center",
  },
  {
    title: "工单状态",
    dataIndex: "resultDesc",
    key: "resultDesc",
    align: "center",
  },
  {
    title: "申请人",
    dataIndex: "propose",
    key: "propose",
    align: "center",
  },
  {
    title: "运维负责人",
    dataIndex: "opsOwner",
    key: "opsOwner",
    align: "center",
  },
  {
    title: "申请日期",
    dataIndex: "ctime",
    key: "ctime",
    align: "center",
    sorter: (a, b) => a.age - b.age,
  },
  {
    title: "详情",
    key: "detail",
    dataIndex: "detail",
    fixed: "right",
    align: "center",
    render: (text, record, index) => {
      return <OrderDetailDrawer title={"详情"} orderID={record.orderID} orderType={record.orderType} />;
    },
  },
];

export default class DoneOrder extends Component {
  state = {
    dataSource: [],
    pageSize: 12,
    page: 1,
    total: 0
  };
  componentDidMount() {
    this.requestMyDonePageOrder()
  }

  requestMyDonePageOrder = () => {
    requestMyDoneOrder({ page: this.state.page, page_size: this.state.pageSize }).then((data) => {
      var orders = data.orders.map((item, index) => {
        return {
          key: index,
          orderID: item.order_id,
          orderType:item.order_type,
          orderTypeName: item.order_type_name,
          totalStageNum: item.total_stage_num,
          currentStage: item.current_stage,
          resultDesc: item.result_desc,
          propose: item.proposer_email,
          opsOwner: item.ops_owner_email,
          ctime: item.apply_datetime,
          trace: "action",
        };
      });
      this.setState({ dataSource: orders, total: data.total });
    });
  }

  changePage = (page, pageSize) => {
    this.setState({ page: page }, this.requestMyDonePageOrder)
  }
  changeShowSize = (current, size) => {
    this.setState({ page: 1, pageSize: size }, this.requestMyDonePageOrder)
  }

  render() {
    return (
      <div>
        <DoneTable
          className="done-order-table"
          dataSource={this.state.dataSource}
          columns={columns}
          size={"middle"}
          pagination={false}
        />
        <OrderPagination
          size="small"
          showQuickJumper
          showSizeChanger
          defaultCurrent={1}
          total={this.state.total}
          current={this.state.page}
          pageSize={this.state.pageSize}
          pageSizeOptions={[12, 20, 30, 50, 100]}
          onChange={this.changePage}
          onShowSizeChange={this.changeShowSize}
        />
      </div>

    );
  }
}
