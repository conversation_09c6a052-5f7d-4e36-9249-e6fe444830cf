import styled from 'styled-components';

export const AuditedTable = styled.div`
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  
  // 确保表格在容器内居中且有合适间距
  .ant-table {
    border-radius: 6px;
  }
  
  // 表格头部样式调整
  .ant-table-thead > tr > th {
    background-color: #fafafa;
    font-weight: 600;
    text-align: center;
  }
  
  // 表格行样式
  .ant-table-tbody > tr > td {
    padding: 12px 8px;
  }
  
  // 悬停效果
  .ant-table-tbody > tr:hover > td {
    background-color: #f5f5f5;
  }
`;
