import React, { Component } from 'react';
import { Pagination } from 'antd';
import { PaginationContainer } from './styles';

class OrderPagination extends Component {
  constructor(props) {
    super(props);
    this.handlePageChange = this.handlePageChange.bind(this);
    this.handlePageSizeChange = this.handlePageSizeChange.bind(this);
  }

  handlePageChange(page) {
    // 页码变更回调，通知父组件
    if (this.props.onPageChange) {
      this.props.onPageChange(page);
    }
    
    // 页码跳转后让输入框失去焦点
    setTimeout(() => {
      const activeElement = document.activeElement;
      if (activeElement && activeElement.blur) {
        activeElement.blur();
      }
    }, 100);
  }

  handlePageSizeChange(current, size) {
    // 每页数量变更回调，通知父组件
    if (this.props.onPageSizeChange) {
      this.props.onPageSizeChange(current, size);
    }
  }

  render() {
    const {
      current = 1,
      pageSize = 10,
      total = 0,
      showSizeChanger = true,
      showQuickJumper = true,
      showTotal,
      pageSizeOptions = ['10', '20', '50', '100'],
    } = this.props;

    return (
      <PaginationContainer>
        <Pagination
          current={current}
          pageSize={pageSize}
          total={total}
          showSizeChanger={showSizeChanger}
          showQuickJumper={showQuickJumper}
          showTotal={showTotal || ((total, range) => `共 ${total} 条记录，显示第 ${range[0]}-${range[1]} 条`)}
          pageSizeOptions={pageSizeOptions}
          onChange={this.handlePageChange}
          onShowSizeChange={this.handlePageSizeChange}
        />
      </PaginationContainer>
    );
  }
}

export default OrderPagination;
