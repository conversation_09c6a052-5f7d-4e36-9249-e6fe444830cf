import styled from 'styled-components';

export const PaginationContainer = styled.div`
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
  align-items: center;

  .ant-pagination {
    display: flex;
    align-items: center;
  }

  // 分页器样式微调
  .ant-pagination-item {
    border-radius: 4px;
  }

  .ant-pagination-item-active {
    border-color: #1890ff;
    background-color: #1890ff;
  }

  .ant-pagination-item-active a {
    color: #fff;
  }

  // 页码输入框样式
  .ant-pagination-options-quick-jumper input {
    border-radius: 4px;
  }

  // 每页显示数量选择器样式
  .ant-select-selector {
    border-radius: 4px;
  }

  // 响应式设计：小屏幕时调整布局
  @media (max-width: 768px) {
    justify-content: center;
    flex-direction: column;
    gap: 8px;

    .ant-pagination {
      flex-wrap: wrap;
      justify-content: center;
    }
  }
`;
