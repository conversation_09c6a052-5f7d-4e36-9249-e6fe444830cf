import { Component } from "react";
import { <PERSON>er, <PERSON><PERSON>, Divider, Descriptions, Modal, message } from "antd";
import { MenuFoldOutlined } from "@ant-design/icons";
import { Steps, Table, Input } from "antd";
import PropTypes from "prop-types";
import {
  UserOutlined,
  SolutionOutlined,
  LoadingOutlined,
  SmileOutlined,
  CloseCircleOutlined,
} from "@ant-design/icons";
import { requestFlowApproval, requestFlowAuditTurn, requestOrderDetail, requestUpdateOrderRemark } from "@/request/api";
import styled from "styled-components";
import Cookies from "js-cookie";

const { TextArea } = Input;
// css-js start ↓↓↓
const OrderDetailHeader = styled.div`
  font-size: x-large;
  font-weight: bolder;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  justify-content: space-between;
  align-items: center;
`
const OrderAuditAction = styled.div`
  width: 170px;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  justify-content: space-between;
  align-items: center;
`
const OrderFlowInfoHeader = styled.div`
  margin-top: 24px;
  font-size: large;
  font-weight: bolder;
`
const FlowInfoTable = styled(Table)`
  margin-top: 12px;
`
const TurnAuditDiv = styled.div`
  display: flex;
  flex-direction:row;
  flex-wrap: nowrap;
  justify-content: center;
  align-items: center;
  white-space: nowrap;
`
// css-js end   ↑↑↑

const { Step } = Steps;

const columns = [
  {
    title: "节点ID",
    dataIndex: "stageNum",
    key: "stageNum",
    align: "center",
  },
  {
    title: "节点名称",
    dataIndex: "stageName",
    key: "stageName",
    align: "center",
  },
  {
    title: "节点处理人",
    dataIndex: "stageOperator",
    key: "stageOperator",
    align: "center",
  },
  {
    title: "结果",
    dataIndex: "stageResult",
    key: "stageResult",
    align: "center",
  },
];

export default class OrderDetailDrawer extends Component {
  static propTypes = {
    orderID: PropTypes.string.isRequired,
    orderType: PropTypes.string.isRequired,
  };
  // static defaultProps = { cnMap: {} };
  state = {
    auditButtonVisible: false,
    turnAuditButtonVisible: false,
    drawerVisible: false,
    auditVisible: false,
    turnAuditVisible: false,
    auditEmail: "",
    remark: "",
    stageInfos: [],
    orderInfo: { info: {} },
    stageResultMap: {
      0: "wait",
      1: "finish",
      2: "error",
    },
    orderResultMap: {
      0: "wait",
      1: "finish",
      2: "wait",
    },
    columnData: [],
    cnMap: {
      "common": {
        "title": [3, "标题"],
        "ops_audit_email": [3, "运维审批人"],
        "apply_msg": [6, "申请理由"],
      },
      "sql_audit_execute": {
        "db_host": [3, "数据库IP"],
        "check_md5": [3, "校验ID"],
        "sql": [6, "sql语句"]
      },
      "sql_audit_file_execute": {
        "db_host": [3, "数据库IP"],
        "check_md5": [3, "校验ID"],
        "sql": [6, "sql语句"]
      },
      "server_jump_impower": {
        "server_ip": [6, "申请登陆权限IP"],
        "apply_msg": [6, "申请理由"],
      },
      "pointed_approver": {
        "approver_email": [6, "审批人"],
        "apply_msg": [6, "申请理由"]
      },
      "cdn_create_execute": {
        "approver_email": [6, "审批人"],
        "apply_msg": [6, "申请理由"]
      },
      "domain_resolve_execute": {
        "approver_email": [6, "审批人"],
        "apply_msg": [6, "申请理由"]
      },
      "long_term_token_new_refresh": {
        "approver_email": [6, "审批人"],
        "apply_msg": [6, "申请理由"]
      },
      "long_term_token_view": {
        "approver_email": [6, "审批人"],
        "apply_msg": [6, "申请理由"]
      }
    }
  };
  getCnMap = (orderType) => {
    if (orderType in this.state.cnMap) {
      return this.state.cnMap[orderType]
    }
    // console.warn("order-type(" + orderType + ") detail display has not been configed.")
    return {
      // "title": [6, "标题"],
      "apply_msg": [6, "申请理由"]
    }

  }
  componentDidUpdate(prevProps, prevState) {
    if (prevState.drawerVisible === false && prevState.drawerVisible !== this.state.drawerVisible) {
      let userEmail = Cookies.get("user_email")
      requestOrderDetail({ order_id: this.props.orderID })
        .then((data) => {
          if (data === null) {
            return;
          }
          const stageInfos = data.stage_infos;
          const orderInfo = data.order_info;
          
          // 先重置按钮状态，避免缓存问题
          let auditButtonVisible = false;
          let turnAuditButtonVisible = false;
          
          // 审批按钮显示条件：当前用户是操作员且工单未完成
          if (stageInfos[orderInfo.current_stage - 1].stage_operator === userEmail && orderInfo.result === 0) {
            auditButtonVisible = true;
          }
          // 审批人变更按钮显示条件：当前用户是操作员且工单未完成且未到最后阶段
          if (stageInfos[orderInfo.current_stage - 1].stage_operator === userEmail && orderInfo.result === 0 && orderInfo.current_stage < orderInfo.total_stage_num) {
            turnAuditButtonVisible = true;
          }
          
          // 统一设置按钮状态
          this.setState({
            auditButtonVisible,
            turnAuditButtonVisible
          });
          console.log(data)
          // 将 order info 字符串转为json对象
          orderInfo.info = JSON.parse(orderInfo.info)
          orderInfo.info["apply_msg"] = orderInfo.apply_msg
          const columnData = stageInfos.map((item, index) => {
            return {
              key: index,
              stageNum: item.stage_num,
              stageName: item.stage_name,
              stageOperator: item.stage_operator,
              stageResult: item.stage_result_desc,
            };
          });
          this.setState({
            stageInfos: stageInfos,
            orderInfo: orderInfo,
            columnData: columnData,
          });
        })
        .catch((err) => console.log(err));
    }
  }

  // 组件销毁前调用，清除一些事件(比如定时事件)
  componentWillUnmount() {
    this.setState = (state, callback) => {
      return
    }
  }

  // 开启左侧抽屉
  showDrawer = () => {
    this.setState({
      drawerVisible: true,
    });
  };
  // 关闭左侧抽屉
  closeDrawer = () => {
    this.setState({
      drawerVisible: false,
    });
  };
  // 开启审批对话框
  showAuditModel = () => {
    this.setState({
      auditVisible: true,
    });
  };
  // 关闭审批对话框
  closeAuditModel = () => {
    this.setState({
      auditVisible: false,
    });
  };
  // 提交审批结果 todo 需要处理审批后的刷新
  handleAudit = (result) => {
    let args = {
      order_id: this.state.orderInfo.order_id,
      stage_num: this.state.orderInfo.current_stage,
      chosen_action: result
    }
    
    // 立即关闭弹窗
    this.closeAuditModel();

    // 审批人变更成功后关闭详情页弹窗
    this.closeDrawer();
    
    // 异步处理后端请求，响应结果在页面顶部显示
    requestFlowApproval(args)
      .then((data) => {
        if (data.resp_common && data.resp_common.ret === 0) {
          message.success('审批成功');
        } else {
          message.error(`审批失败: ${(data.resp_common && data.resp_common.msg) || '未知错误'}`);
        }
      })
      .catch((err) => {
        message.error('审批失败');
        console.log(err);
      });
  }
  // 开启审批人变更对话框
  showTurnAuditVisible = () => {
    this.setState({
      turnAuditVisible: true,
    });
  };
  // 关闭审批人变更对话框
  closeTurnAuditVisible = () => {
    this.setState({
      turnAuditVisible: false,
    });
  };
  handleTurnAudit = () => {
    // 检查邮箱是否为空
    if (!this.state.auditEmail || this.state.auditEmail.trim() === '') {
      message.error('请输入审批人邮箱');
      return;
    }

    // 检查邮箱格式是否正确
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    if (!emailRegex.test(this.state.auditEmail)) {
      message.error('请输入正确的邮箱格式');
      return;
    }
    
    let args = {
      order_id: this.state.orderInfo.order_id,
      stage_num: this.state.orderInfo.current_stage,
      new_operator_email: this.state.auditEmail
    }
    
    // 立即关闭弹窗
    this.closeTurnAuditVisible();

    // 审批人变更成功后关闭详情页弹窗
    this.closeDrawer();
    
    // 异步处理后端请求，响应结果在页面顶部显示
    requestFlowAuditTurn(args)
      .then((data) => {
        if (data.resp_common && data.resp_common.ret === 0) {
          message.success('审批人变更成功');
        } else {
          message.error(`审批人变更失败: ${(data.resp_common && data.resp_common.msg) || '未知错误'}`);
        }
      })
      .catch((err) => {
        message.error('审批人变更失败');
        console.log(err);
      });
  }
  handleAuditEmailChange = (e) => {
    this.setState({
      auditEmail: e.target.value,
    });
  };
  saveRemark = (e) => {
    requestUpdateOrderRemark({
      order_id: this.state.orderInfo.order_id,
      remark: this.state.remark.trim()
    })
  }
  render() {
    return (
      <>

        <Button type="primary" onClick={this.showDrawer}>
          <MenuFoldOutlined />
          {this.props.title}
        </Button>
        <Drawer
          width={800}
          placement="right"
          closable={false}
          onClose={this.closeDrawer}
          open={this.state.drawerVisible}
        >
          <OrderDetailHeader>
            审批流程跟踪
            <OrderAuditAction className={(this.state.auditButtonVisible || this.state.turnAuditButtonVisible) ? "" : "hide"}>
              <Button 
                type="primary" 
                onClick={this.showTurnAuditVisible}
                className={this.state.turnAuditButtonVisible ? "" : "hide"}
              >
                审批人变更
              </Button>
              <Button 
                type="primary" 
                onClick={this.showAuditModel}
                className={this.state.auditButtonVisible ? "" : "hide"}
              >
                审批
              </Button>
            </OrderAuditAction>
          </OrderDetailHeader>
          <Divider />
          <Steps>
            <Step status="finish" title="提交" icon={<UserOutlined />} />
            {
              this.state.stageInfos.map((item, index) => {
                // 审批人去除邮箱后缀，减少长度
                var reg = new RegExp("@.*", "g");
                item.stage_operator = item.stage_operator.replace(reg, "")
                if (item.stage_result > 1) {
                  return (
                    <Step
                      status={this.state.stageResultMap[item.stage_result]}
                      title={item.stage_name}
                      // subTitle={"("+item.stage_operator+")"}
                      description={item.stage_operator}
                      icon={<CloseCircleOutlined />}
                      key={index}
                    />
                  );
                }
                if (
                  this.state.orderInfo.current_stage === index + 1 &&
                  item.stage_result === 0
                ) {
                  return (
                    <Step
                      status="finish"
                      title={item.stage_name}
                      // subTitle={"("+item.stage_operator+")"}
                      description={item.stage_operator}
                      icon={<LoadingOutlined />}
                      key={index}
                    />
                  );
                }
                return (
                  <Step
                    status={this.state.stageResultMap[item.stage_result]}
                    title={item.stage_name}
                    // subTitle={"("+item.stage_operator+")"}
                    description={item.stage_operator}
                    icon={<SolutionOutlined />}
                    key={index}
                  />
                );
              })
            }
            <Step
              status={this.state.orderResultMap[this.state.orderInfo.result]}
              title="Done"
              icon={<SmileOutlined />}
            />
          </Steps>

          <FlowInfoTable
            dataSource={this.state.columnData}
            columns={columns}
            pagination={false}
          />
          <OrderFlowInfoHeader>
            工单详情
          </OrderFlowInfoHeader>
          <Divider />
          <Descriptions bordered layout="vertical" column={6} labelStyle={{ "fontWeight": "bolder", "fontSize": 15 }}>
            {
              Object.keys(this.getCnMap(this.props.orderType)).map((label, index) => {
                // const cnMap = this.state.cnMap[this.props.orderType]
                const cnMap = this.getCnMap(this.props.orderType)
                const spanNum = cnMap[label][0]
                const cnName = cnMap[label][1]
                const content = this.state.orderInfo.info[label]
                return (
                  <Descriptions.Item
                    key={index}
                    label={cnName}
                    span={spanNum}
                    style={{ "whiteSpace": "pre-line" }}
                  >
                    {content}
                  </Descriptions.Item>
                )
              })
            }
          </Descriptions>
          <OrderFlowInfoHeader>
            备注
          </OrderFlowInfoHeader>
          <Divider />
          <TextArea rows={4} placeholder="输入信息" maxLength={6}
            value={this.state.remark}
            onChange={(e) => this.setState({ remark: e.target.value })}
            onBlur={this.saveRemark}
            autoSize={{
              minRows: 10,
              maxRows: 20,
            }} />
          <Divider />
        </Drawer>
        <Modal
          title="审批工单"
          open={this.state.auditVisible}
          onCancel={this.closeAuditModel}
          footer={[
            <Button key="audit-reject" type="primary" danger onClick={() => this.handleAudit("no")}>
              驳回
            </Button>,
            <Button key="audit-agree" type="primary" onClick={() => this.handleAudit("yes")}>
              同意
            </Button>
          ]}
        >
          <h1>申请理由：</h1>
          <p>{this.state.orderInfo.apply_msg}</p>
        </Modal>
        <Modal
          title="审批人变更"
          open={this.state.turnAuditVisible}
          okText="提交"
          onOk={this.handleTurnAudit}
          onCancel={this.closeTurnAuditVisible}
        >
          <TurnAuditDiv>
            新审批人邮箱：
            <Input
              placeholder="请输入OA邮箱"
              size="middle"
              prefix={<UserOutlined />}
              value={this.state.auditEmail}
              onChange={this.handleAuditEmailChange}
            />
          </TurnAuditDiv>
        </Modal>
      </>
    );
  }
}
