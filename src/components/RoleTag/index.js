import React from 'react';
import { Tag } from 'antd';
import './style.css';

/**
 * RoleTag 组件 - 用于展示工单身份标签
 * @param {Object} props
 * @param {string} props.roleType - 角色类型枚举值
 * @returns {JSX.Element|null} 返回对应的标签组件或null
 */
const RoleTag = ({ roleType }) => {
  // 角色映射配置
  const ROLE_CONFIG = {
    APPLICANT: {
      text: '[我申请的]',
      color: 'default'
    },
    CC_TO_ME: {
      text: '[抄送我的]',
      color: 'default'
    },
    TO_BE_APPROVED: {
      text: '[待我审批]',
      color: 'orange'
    },
    ALREADY_APPROVED: {
      text: '[我已审批]',
      color: 'default'
    }
  };

  // 根据roleType获取配置
  const config = ROLE_CONFIG[roleType];

  // 如果找不到配置，返回null（不渲染任何内容）
  if (!config) {
    return null;
  }

  return (
    <Tag 
      className="role-tag" 
      color={config.color}
    >
      {config.text}
    </Tag>
  );
};

export default RoleTag;
