import React, { useState, useEffect } from 'react';
import { Input, Tag } from 'antd';
import { validateUser } from '../../request/api';
import './style.css';

const CcInput = ({ value = [], onChange, currentUserEmail }) => {
  const [inputValue, setInputValue] = useState('');
  const [errorMsg, setErrorMsg] = useState(null);

  // 清除错误提示的定时器
  useEffect(() => {
    if (errorMsg) {
      const timer = setTimeout(() => {
        setErrorMsg(null);
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [errorMsg]);

  // 简单的邮箱格式验证
  const isValidEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  // 处理键盘事件
  const handleKeyDown = async (event) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();

      const email = inputValue.trim();
      if (!email) return;

      // 基本邮箱格式验证
      if (!isValidEmail(email)) {
        setErrorMsg('请输入有效的邮箱格式');
        triggerShakeAnimation();
        return;
      }

      // 前端自校验：检查是否为当前用户
      if (email === currentUserEmail) {
        setErrorMsg('不能抄送给自己');
        triggerShakeAnimation();
        return;
      }

      // 前端自校验：检查是否已在列表中
      if (value.some(user => user.email === email)) {
        setErrorMsg('该用户已被添加');
        triggerShakeAnimation();
        return;
      }

      // 检查是否达到上限
      if (value.length >= 50) {
        setErrorMsg('抄送人数已达上限（50人）');
        triggerShakeAnimation();
        return;
      }

      // 调用后端校验
      try {
        const response = await validateUser(email);
        const newUser = {
          open_id: response.open_id,
          email: email,
          display_text: `${response.name} (${response.department})`
        };

        // 调用父组件的onChange回调
        onChange([...value, newUser]);

        // 清空输入框和错误提示
        setInputValue('');
        setErrorMsg(null);
      } catch (error) {
        setErrorMsg(error);
        triggerShakeAnimation();
      }
    }
  };

  // 触发抖动动画
  const triggerShakeAnimation = () => {
    const inputElement = document.querySelector('.cc-input-container .ant-input');
    if (inputElement) {
      inputElement.classList.add('cc-input-shake');
      setTimeout(() => {
        inputElement.classList.remove('cc-input-shake');
      }, 600);
    }
  };

  // 处理移除用户
  const handleRemove = (emailToRemove) => {
    const newList = value.filter(user => user.email !== emailToRemove);
    onChange(newList);
  };

  // 处理输入变化
  const handleInputChange = (e) => {
    setInputValue(e.target.value);
    // 用户开始输入时清除错误提示
    if (errorMsg) {
      setErrorMsg(null);
    }
  };

  return (
    <div className="cc-input-container">
      <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
        <Input
          placeholder="请输入抄送人邮箱，按回车或空格确认"
          value={inputValue}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          disabled={value.length >= 50}
          style={{ flex: 1 }}
        />
        <span className="cc-count">{value.length}/50</span>
      </div>

      {/* 错误提示区域 */}
      <div className={`cc-error-message ${errorMsg ? 'show' : ''}`}>
        {errorMsg}
      </div>

      {/* 已选用户标签区域 */}
      {value.length > 0 && (
        <div className="cc-selected-users">
          {value.map((user) => (
            <Tag
              key={user.email}
              closable
              onClose={() => handleRemove(user.email)}
            >
              {user.display_text}
            </Tag>
          ))}
        </div>
      )}
    </div>
  );
};

export default CcInput;
