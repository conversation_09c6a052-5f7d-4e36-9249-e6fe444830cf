import { Component } from "react";
import { Routes, Route } from "react-router-dom";

import SidebarNav from "@/common/baseLayout/sidebar/index.";
import DoingOrder from "@/pages/doingOrder";
import DoneOrder from "@/pages/doneOrder";
import AuditOrder from "@/pages/auditOrder";


// 头部导航栏路由
const headerRules = [
  {
    path: "/new-order/*",
    componet: <SidebarNav />,
    desc: "工单申请",
    id: "/new-order",
  },
  {
    path: "/doing-order",
    componet: <DoingOrder />,
    desc: "进行中的工单",
    id: "/doing-order",
  },
  {
    path: "/done-order",
    componet: <DoneOrder />,
    desc: "已完结工单",
    id: "/done-order",
  }, 
  {
    path: "/audit-order",
    componet: <AuditOrder />,
    desc: "我的审批工单",
    id: "/audit-order",
  },
];
export default class HeaderRouter extends Component {

  render() {
    return (
      <Routes>
        {headerRules.map((rule) => {
          return (
            <Route path={rule.path} element={rule.componet} key={rule.id} />
          );
        })}
      </Routes>
    );
  }
}
export { headerRules };
