# src/pages/auditOrder/index.js

## 类/方法/函数详解

### AuditOrder
#### componentDidMount()
- **用途**: 在组件挂载后立即获取第一页的待审批工单数据。
- **输入参数**: 无。
- **输出**: 无。
- **实现要点**: 直接调用 `this.requestMyAuditPageOrder()` 方法。

#### parseOrderTitle(info, orderId)
- **用途**: 一个安全的工具方法，用于从工单的 `info` 字段（一个JSON字符串）中解析出工单标题。
- **输入参数**: 
  - `info` (string): 包含工单元信息的JSON字符串。
  - `orderId` (string|number): 工单ID，用于在解析失败时生成一个备用标题。
- **输出**: (string) 解析出的工单标题，或一个备用标题。
- **实现要点**:
  1. 使用 `try-catch` 块来包裹 `JSON.parse`，防止因 `info` 格式错误而导致程序崩溃。
  2. 如果 `info` 存在且解析成功，返回 `parsedInfo.title`。
  3. 如果解析失败或 `info` 为空，返回一个格式为 `工单-${orderId}` 的字符串。

#### requestMyAuditPageOrder()
- **用途**: 核心的数据请求方法，根据当前的状态（页码、页面大小、搜索参数）获取待我审批的工单列表。
- **输入参数**: 无（从 `this.state` 中获取）。
- **输出**: 无（通过 `setState` 更新UI）。
- **实现要点**:
  1. 设置 `loading` 状态为 `true`。
  2. 从 `state` 中解构出 `pageNum`, `pageSize`, `searchParams`。
  3. 构造请求参数，包含分页信息、搜索条件（工单ID、标题、日期范围）和工单类型筛选。
  4. 调用 `requestMyAuditOrderWithSearch` API。
  5. **成功处理**:
     - 检查响应体 `resp_common.ret` 是否为 `0`。
     - 使用 `map` 方法转换后端返回的 `orders` 数组为表格所需的格式，并使用 `parseOrderTitle` 安全地设置标题。
     - 如果后端响应中包含 `order_types`，则调用 `updateOrderTypeFiltersFromResponse` 更新筛选选项；否则，尝试从当前返回的工单列表中提取筛选选项。
     - 使用 `setState` 更新 `dataSource`, `total`, 和 `loading` 状态。
  6. **失败处理**:
     - 如果API返回错误或请求本身 `catch` 到异常，则清空表格数据，并将 `loading` 设为 `false`。
     - 如果当前没有工单类型筛选选项，则调用 `useMockOrderTypeFilters` 使用备用数据填充筛选器，保证UI可用性。

#### handlePageChange(pageNum)
- **用途**: 处理分页组件的页码变化事件。
- **输入参数**: `pageNum` (number): 新的页码。
- **输出**: 无。
- **实现要点**: 调用 `setState` 更新 `pageNum`，并在回调函数中调用 `requestMyAuditPageOrder()` 以获取新一页的数据。

#### handlePageSizeChange(current, size)
- **用途**: 处理分页组件的每页显示数量变化事件。
- **输入参数**: 
  - `current` (number): 当前页码。
  - `size` (number): 新的页面大小。
- **输出**: 无。
- **实现要点**: 调用 `setState` 将 `pageNum` 重置为1并更新 `pageSize`，然后在回调函数中调用 `requestMyAuditPageOrder()`。

#### handleSearch(searchParams, hasValidationErrors)
- **用途**: 响应 `SearchForm` 组件的搜索事件。
- **输入参数**:
  - `searchParams` (object): 包含 `orderId`, `title`, `appliedStartDate`, `appliedEndDate` 的对象。
  - `hasValidationErrors` (boolean): 指示搜索条件是否存在校验错误。
- **输出**: 无。
- **实现要点**:
  1. 如果 `hasValidationErrors` 为 `true`，则直接返回，不执行搜索。
  2. 合并新的搜索条件和已有的 `orderTypes` 筛选条件。
  3. 使用 `setState` 更新 `searchParams` 并将 `pageNum` 重置为1，然后在回调中调用 `requestMyAuditPageOrder()`。

#### handleOrderTypeFilterChange(selectedTypes)
- **用途**: 处理表格头部的工单类型筛选变化。
- **输入参数**: `selectedTypes` (array): 被选中的工单类型值数组。
- **输出**: 无。
- **实现要点**:
  1. 更新 `searchParams` 中的 `orderTypes`。
  2. 使用 `setState` 更新 `searchParams` 并将 `pageNum` 重置为1，然后在回调中调用 `requestMyAuditPageOrder()`。

#### extractOrderTypeFilters(orders)
- **用途**: 从工单列表数据中提取唯一的工单类型，用于生成表格的筛选选项。
- **输入参数**: `orders` (array): 从API获取的原始工单数组。
- **输出**: 无（通过 `setState` 更新 `orderTypeFilters`）。
- **实现要点**:
  1. 使用 `Map` 来收集唯一的 `order_type` 和 `order_type_name` 对，以去重。
  2. 将 `Map` 转换为 `antd` Table `filter` 所需的 `{ text, value }` 格式数组。
  3. 按中文名称 (`order_type_name`) 对筛选选项进行排序。
  4. 如果提取成功，则更新 `orderTypeFilters` 状态；否则，调用 `useMockOrderTypeFilters`。

#### updateOrderTypeFiltersFromResponse(orderTypes)
- **用途**: 使用后端直接返回的工单类型列表来更新筛选选项。
- **输入参数**: `orderTypes` (array): 后端返回的工单类型字符串数组。
- **输出**: 无。
- **实现要点**:
  1. 遍历 `orderTypes` 数组。
  2. 对每个 `orderType`，调用 `getOrderTypeDisplayName` 获取其显示名称。
  3. 构造成 `{ text, value }` 格式的数组并更新 `orderTypeFilters` 状态。

#### getOrderTypeDisplayName(orderType)
- **用途**: 将内部的工单类型标识符（如 `ks_server_apply`）映射为用户友好的显示名称（如“金山云服务器申请”）。
- **输入参数**: `orderType` (string): 工单类型标识符。
- **输出**: (string) 对应的显示名称，如果未找到映射则返回原标识符。
- **实现要点**: 内部维护一个巨大的 `typeNameMap` 对象作为映射表。

#### useMockOrderTypeFilters()
- **用途**: 在无法从API获取工单类型筛选选项时的后备方案，提供一个硬编码的、完整的筛选列表。
- **输入参数**: 无。
- **输出**: 无。
- **实现要点**: 定义一个包含所有已知工单类型的数组，并用它来更新 `orderTypeFilters` 状态。

#### 类用途
`AuditOrder` 组件构建了一个“待我审批”的工单管理页面。它负责展示所有需要当前登录用户审批的工单列表。核心功能包括：分页加载工单数据、根据工单ID/标题/申请日期进行搜索、按工单类型进行筛选。该组件具有良好的用户体验，如加载状态提示（骨架屏）、错误处理和在API异常时提供备用筛选数据。

## 文件用途
1. **代码用途概述**: 该文件实现了待审批工单的展示、搜索、筛选和分页功能。
2. **使用场景和流程**:
   - 用户进入“待我审批”页面，组件首次加载，调用API获取第一页工单数据和筛选选项。
   - 数据返回前，页面显示骨架屏。
   - 数据返回后，工单列表在表格中展示，工单类型筛选器被填充。
   - 用户可以在顶部的搜索栏输入工单ID、标题或选择日期范围，点击搜索按钮，触发 `handleSearch`，重新请求过滤后的数据。
   - 用户可以点击表格头部的工单类型筛选器，选择一个或多个类型，触发 `handleOrderTypeFilterChange`，重新请求数据。
   - 用户可以点击底部的分页控件，切换页码或每页显示数量，触发 `handlePageChange` 或 `handlePageSizeChange`，请求相应页面的数据。

## 调用时序图
```mermaid
sequenceDiagram
    participant User
    participant AuditOrder as "页面(AuditOrder)"
    participant SearchForm as "组件(SearchForm)"
    participant OrderTable as "组件(OrderTable)"
    participant API as "后端API"

    User->>AuditOrder: 访问“待我审批”页面
    activate AuditOrder
    AuditOrder->>AuditOrder: componentDidMount()
    AuditOrder->>API: requestMyAuditOrderWithSearch()
    AuditOrder->>OrderTable: 显示Loading骨架屏

    API-->>AuditOrder: 返回工单数据和筛选列表
    AuditOrder->>AuditOrder: setState(dataSource, orderTypeFilters)
    AuditOrder->>OrderTable: 渲染工单数据和筛选器
    deactivate AuditOrder

    User->>SearchForm: 输入搜索条件并点击搜索
    activate SearchForm
    SearchForm->>AuditOrder: onSearch(searchParams)
    deactivate SearchForm
    
    activate AuditOrder
    AuditOrder->>API: requestMyAuditOrderWithSearch(searchParams)
    API-->>AuditOrder: 返回搜索结果
    AuditOrder->>OrderTable: 重新渲染数据
    deactivate AuditOrder

    User->>OrderTable: 点击工单类型筛选
    activate OrderTable
    OrderTable->>AuditOrder: onOrderTypeFilterChange(selectedTypes)
    deactivate OrderTable

    activate AuditOrder
    AuditOrder->>API: requestMyAuditOrderWithSearch(..., orderTypes)
    API-->>AuditOrder: 返回筛选结果
    AuditOrder->>OrderTable: 重新渲染数据
    deactivate AuditOrder
```