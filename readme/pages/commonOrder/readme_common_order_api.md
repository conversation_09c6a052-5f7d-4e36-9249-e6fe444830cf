# src/pages/commonOrder/index.js

## 类/方法/函数详解

### CommonOrderForm
#### constructor(props)
- **用途**: 组件构造函数，初始化时预加载审批领导数据。
- **输入参数**: `props` (React组件属性)。
- **输出**: 无。
- **实现要点**:
  1. 调用 `super(props)`。
  2. 调用 `requestLeaderEmail` 和 `requestSuperLeaderEmails` 并将返回的 Promise 存放在 `this.leaderPromise` 和 `this.superLeaderPromise` 中，以便在 `componentDidMount` 中可以更快地使用数据，提升用户体验。

#### componentDidMount()
- **用途**: 组件挂载后，获取并设置表单所需的初始数据。
- **输入参数**: 无。
- **输出**: 无。
- **实现要点**:
  1. 使用构造函数中预加载的 `this.leaderPromise` 来设置部门领导邮件列表。
  2. 并行发起 `requestOpsMember`（获取运维审批人列表）和 `requestOpsMemberDutySpectrum`（获取业务类型与运维人员的映射关系）的请求。
  3. 使用 `Promise.all` 等待所有数据加载完成，并处理可能的错误。
  4. 将获取到的数据更新到组件的 `state` 中。

#### handleTitleChange(e)
- **用途**: 响应工单标题输入框的变化。
- **输入参数**: `e` (事件对象)，`e.target.value` 包含输入框的最新值。
- **输出**: 无。
- **实现要点**: 调用 `this.setState` 更新 `title` 状态。

#### handleApplyMsgChange(e)
- **用途**: 响应需求描述文本域的变化。
- **输入参数**: `e` (事件对象)，`e.target.value` 包含文本域的最新值。
- **输出**: 无。
- **实现要点**: 调用 `this.setState` 更新 `apply_msg` 状态。

#### handleAuditEmailChange(value)
- **用途**: 响应运维审批人下拉框的选择变化。
- **输入参数**: `value` (被选中的运维审批人邮箱)。
- **输出**: 无。
- **实现要点**: 调用 `this.setState` 更新 `selected_ops_audit_email` 状态。

#### handleDutyChange(value)
- **用途**: 响应业务类型下拉框的选择变化。
- **输入参数**: `value` (被选中的业务类型)。
- **输出**: 无。
- **实现要点**:
  1. 如果选择的是 “新系统上线资源申请”，则在需求描述中自动填充预设的模板文字。
  2. 从 `dutySpectrumMemberMap` 中找到该业务类型对应的运维审批人列表。
  3. 随机选择一个运维审批人并更新 `selected_ops_audit_email` 状态。

#### handleFileUpload(options)
- **用途**: 处理附件上传。这是一个自定义的上传请求处理函数，用于 `antd` 的 `Upload` 组件。
- **输入参数**: `options` (Upload组件提供的对象，包含 `file`, `onSuccess`, `onError`, `onProgress` 等)。
- **输出**: 无。
- **实现要点**:
  1. 使用 `getFileContent` 读取文件内容。
  2. 使用 `CryptoJS.MD5` 计算文件内容的 MD5 值，用于生成唯一的文件路径。
  3. 请求腾讯云 COS 的临时密钥 (`requestTxCamCosAuth`)。
  4. 使用 `cos-js-sdk-v5` 将文件上传到指定的 Bucket。
  5. 在上传过程中，调用 `options.onProgress` 更新上传进度。
  6. 上传成功后，将文件信息（路径和名称）添加到 `file_infos` 状态中，并调用 `options.onSuccess`。
  7. 上传失败则调用 `options.onError`。

#### resetHandle()
- **用途**: 重置表单所有输入项到初始状态。
- **输入参数**: 无。
- **输出**: 无。
- **实现要点**: 调用 `this.setState` 将 `title`, `apply_msg`, `order_type` 等状态重置为默认值。

#### handleSubmit()
- **用途**: 处理表单提交，发起创建通用工单的请求。
- **输入参数**: 无。
- **输出**: 无。
- **实现要点**:
  1. 校验标题和需求描述是否为空或过短。
  2. 如果审批模式为“指定审批人”，校验审批人邮箱是否已填写。
  3. 过滤需求描述中的模板文字和非法字符（如 `'"\t`）。
  4. 构造请求参数，包括 `apply_info`, `order_type`, `title`, `apply_msg` 和 `attachment_info`。
  5. 调用 `requestCommonOrder` API 发送请求。
  6. 成功后，使用 `navigateModal` 提示用户并跳转到“进行中的工单”页面。

#### handleOrderTypeChange(value)
- **用途**: 响应审批模式（`Segmented` 组件）的变化。
- **输入参数**: `value` (新的审批模式)。
- **输出**: 无。
- **实现要点**:
  1. 判断新的审批模式是否为“指定审批人”，并设置 `show_pointed_approver` 状态来控制审批人输入框的显示/隐藏。
  2. 如果不是“指定审批人”，则根据不同的模式（部门领导、两级领导、三级领导），从预加载的 `leaderPromise` 或 `superLeaderPromise` 中获取相应的审批人邮箱列表，并更新到 `leader_emails` 状态中。
  3. 更新 `order_type` 状态。

#### 类用途
`CommonOrderForm` 是一个用于创建“通用运维工单”的 React 类组件。它提供了一个功能丰富的表单，允许用户填写工单标题、详细需求，选择不同的审批流程（如部门领导、多级领导、或手动指定审批人），并支持上传附件。该组件集成了数据获取、表单校验、文件上传至云存储以及工单提交等功能。

### withRouter(CommonOrderForm)
- **用途**: 高阶组件，为 `CommonOrderForm` 组件注入路由相关的 props（如 `navigate`），使其能够执行页面跳转。

## 文件用途
1. **代码用途概述**: 该文件实现了一个通用的工单申请页面。用户可以通过这个页面提交需要运维团队处理的各种通用请求。
2. **使用场景和流程**:
   - 用户访问该页面，表单会预加载和显示默认的审批人和业务类型。
   - 用户选择业务类型，系统会推荐对应的运维审批人。
   - 用户选择审批模式（如“部门领导审批”）。
   - 用户填写工单标题和详细需求描述。
   - (可选) 用户上传相关附件，附件会被上传到腾讯云 COS。
   - 用户点击“提交”按钮，表单进行校验，然后将工单数据发送到后端。
   - 提交成功后，页面自动跳转到“进行中的工单”列表。

## 调用时序图
```mermaid
sequenceDiagram
    participant User
    participant CommonOrderForm as "页面(CommonOrderForm)"
    participant COS as "腾讯云COS"
    participant API as "后端API"

    User->>CommonOrderForm: 访问通用工单页面
    activate CommonOrderForm
    CommonOrderForm->>API: 请求领导/运维/业务数据
    API-->>CommonOrderForm: 返回数据
    CommonOrderForm->>CommonOrderForm: 渲染表单

    User->>CommonOrderForm: 填写表单内容
    User->>CommonOrderForm: 上传附件
    CommonOrderForm->>API: 请求COS临时密钥
    API-->>CommonOrderForm: 返回密钥
    CommonOrderForm->>COS: 上传文件
    COS-->>CommonOrderForm: 上传成功

    User->>CommonOrderForm: 点击“提交”
    CommonOrderForm->>CommonOrderForm: 校验表单数据
    alt 校验通过
        CommonOrderForm->>API: requestCommonOrder(工单数据)
        API-->>CommonOrderForm: 提交成功
        CommonOrderForm->>User: 提示成功并跳转页面
    else 校验失败
        CommonOrderForm->>User: 显示警告信息
    end
    deactivate CommonOrderForm
```