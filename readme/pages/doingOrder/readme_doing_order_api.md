# src/pages/doingOrder/index.js

## 类/方法/函数详解

### DoingOrder
#### componentDidMount()
- **用途**: 在组件首次挂载后，调用 `requestMyDoingOrder` API 获取所有进行中的工单数据。
- **输入参数**: 无。
- **输出**: 无。
- **实现要点**:
  1. 调用 `requestMyDoingOrder` API，硬编码分页参数为 `page: 1, page_size: 100`，意图获取所有（或前100条）进行中的工单。
  2. API 返回成功后，使用 `map` 方法遍历返回的 `orders` 数组。
  3. 将每个工单对象转换为 `antd` `Table` 组件所需的格式，例如拼接“当前节点 / 节点名称”字段。
  4. 调用 `this.setState` 更新 `dataSource`，从而刷新表格数据。

#### 类用途
`DoingOrder` 是一个 React 类组件，用于展示当前用户所有“进行中”的工单。它通过调用后端API获取数据，并使用 `antd` 的 `Table` 组件将工单列表展示出来。与 `DoneOrder` 不同，当前版本的 `DoingOrder` 没有实现分页功能，它一次性加载所有进行中的工单。每一行同样提供了一个“详情”入口，以便用户查看工单的完整信息。

## 文件用途
1. **代码用途概述**: 该文件实现了一个“进行中工单”的列表展示页面。
2. **使用场景和流程**:
   - 用户导航到“进行中工单”页面，该页面通常是登录后的默认首页。
   - 组件挂载后，自动请求所有进行中的工单数据。
   - 数据返回后，在表格中渲染出工单列表。
   - 用户可以点击任意工单行末的“详情”按钮，弹出一个抽屉组件来查看该工单的详细信息和当前进度。

## 调用时序图
```mermaid
sequenceDiagram
    participant User
    participant DoingOrder as "页面(DoingOrder)"
    participant OrderDetailDrawer as "组件(OrderDetailDrawer)"
    participant API as "后端API"

    User->>DoingOrder: 访问“进行中工单”页面
    activate DoingOrder
    DoingOrder->>DoingOrder: componentDidMount()
    DoingOrder->>API: requestMyDoingOrder({page: 1, page_size: 100})
    
    API-->>DoingOrder: 返回工单数据
    DoingOrder->>DoingOrder: setState({dataSource: orders})
    DoingOrder->>DoingOrder: 渲染表格
    deactivate DoingOrder
    
    User->>DoingOrder: 点击某工单的“详情”
    DoingOrder->>OrderDetailDrawer: 渲染并传入orderID
```