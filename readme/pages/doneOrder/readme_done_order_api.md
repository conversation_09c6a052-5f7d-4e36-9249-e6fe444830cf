# src/pages/doneOrder/index.js

## 类/方法/函数详解

### DoneOrder
#### componentDidMount()
- **用途**: 组件首次挂载后，调用 `requestMyDonePageOrder` 方法获取第一页的已完成工单数据。
- **输入参数**: 无。
- **输出**: 无。
- **实现要点**: `this.requestMyDonePageOrder()`。

#### requestMyDonePageOrder()
- **用途**: 根据当前 `state` 中的页码和页面大小，向后端请求已完成的工单列表。
- **输入参数**: 无 (从 `this.state` 获取 `page` 和 `pageSize`)。
- **输出**: 无 (通过 `setState` 更新UI)。
- **实现要点**:
  1. 调用 `requestMyDoneOrder` API，并传入分页参数。
  2. API 返回成功后，使用 `map` 方法遍历返回的 `orders` 数组。
  3. 将每个工单对象转换为 `antd` `Table` 组件所需的格式，包括添加 `key` 和重命名字段。
  4. 调用 `this.setState` 更新 `dataSource` (表格数据) 和 `total` (总记录数)。

#### changePage(page, pageSize)
- **用途**: 响应分页组件的页码变化事件。
- **输入参数**: 
  - `page` (number): 新的页码。
  - `pageSize` (number): 当前每页数量 (在此方法中未使用)。
- **输出**: 无。
- **实现要点**: 调用 `setState` 更新 `page`，并在回调函数中调用 `requestMyDonePageOrder` 重新获取数据。

#### changeShowSize(current, size)
- **用途**: 响应分页组件的每页显示数量变化事件。
- **输入参数**:
  - `current` (number): 当前页码。
  - `size` (number): 新的页面大小。
- **输出**: 无。
- **实现要点**: 调用 `setState` 将 `page` 重置为1并更新 `pageSize`，然后在回调函数中调用 `requestMyDonePageOrder` 重新获取数据。

#### 类用途
`DoneOrder` 是一个 React 类组件，用于展示当前用户所有已完成的工单。它通过调用后端API获取数据，并使用 `antd` 的 `Table` 组件以列表形式清晰地展示工单信息。该组件还集成了分页功能，允许用户浏览大量的历史工单。在每一行中，都提供了一个“详情”入口，通过 `OrderDetailDrawer` 组件来展示工单的完整信息。

## 文件用途
1. **代码用途概述**: 该文件实现了一个“已完成工单”的列表展示页面。
2. **使用场景和流程**:
   - 用户导航到“已完成工单”页面。
   - 组件挂载后，自动请求第一页的数据。
   - 数据返回后，在表格中渲染出工单列表。
   - 用户可以通过页面底部的分页控件来切换页面或改变每页显示的工单数量，每次操作都会触发新的数据请求。
   - 用户可以点击任意工单行末的“详情”按钮，弹出一个抽屉组件来查看该工单的详细信息。

## 调用时序图
```mermaid
sequenceDiagram
    participant User
    participant DoneOrder as "页面(DoneOrder)"
    participant OrderPagination as "组件(OrderPagination)"
    participant OrderDetailDrawer as "组件(OrderDetailDrawer)"
    participant API as "后端API"

    User->>DoneOrder: 访问“已完成工单”页面
    activate DoneOrder
    DoneOrder->>DoneOrder: componentDidMount()
    DoneOrder->>API: requestMyDoneOrder({page: 1, ...})
    
    API-->>DoneOrder: 返回第一页数据
    DoneOrder->>DoneOrder: setState(dataSource, total)
    DoneOrder->>DoneOrder: 渲染表格和分页
    deactivate DoneOrder
    
    User->>OrderPagination: 点击下一页
    activate OrderPagination
    OrderPagination->>DoneOrder: changePage(newPage)
    deactivate OrderPagination
    
    activate DoneOrder
    DoneOrder->>API: requestMyDoneOrder({page: newPage, ...})
    API-->>DoneOrder: 返回新一页数据
    DoneOrder->>DoneOrder: setState(dataSource)
    DoneOrder->>DoneOrder: 重新渲染表格
    deactivate DoneOrder
    
    User->>DoneOrder: 点击某工单的“详情”
    DoneOrder->>OrderDetailDrawer: 渲染并传入orderID
```